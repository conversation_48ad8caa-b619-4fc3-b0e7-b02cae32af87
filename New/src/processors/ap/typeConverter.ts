/**
 * AP to CC Type Conversion Utilities
 *
 * Handles bidirectional type conversion between AutoPatient (AP) and CliniCore (CC) custom fields.
 * This module provides the inverse conversion logic for the CC to AP mappings found in
 * ccToApCustomFieldsProcessor.ts and customFieldCreator.ts.
 *
 * Key Features:
 * - AP to CC field type mapping (inverse of CC_TO_AP_DATA_TYPE_MAPPING)
 * - Value transformation for boolean fields (AP RADIO Yes/No → CC boolean true/false)
 * - Detection of boolean RADIO fields based on Yes/No options
 * - Graceful handling of unsupported conversions
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logInfo, logWarn } from "@/utils/logger";

/**
 * AP to CC field type mapping
 * Inverse of the CC_TO_AP_DATA_TYPE_MAPPING from customFieldCreator.ts
 */
export const AP_TO_CC_TYPE_MAPPING: Record<string, string> = {
  // Text-based fields
  "TEXT": "text",
  "LARGE_TEXT": "textarea",

  // Numeric fields
  "NUMERICAL": "number",
  "FLOAT": "decimal",
  "MONETORY": "currency",

  // Contact fields
  "PHONE": "phone",

  // Boolean fields - RADIO can be boolean if it has Yes/No options
  "CHECKBOX": "boolean",
  "RADIO": "boolean", // Will be validated by checking options

  // Selection fields
  "SINGLE_OPTIONS": "select",
  "MULTIPLE_OPTIONS": "multiselect",

  // Date/Time fields
  "DATE": "date",

  // File fields
  "FILE_UPLOAD": "file",

  // Signature
  "SIGNATURE": "signature",
};

/**
 * Check if an AP RADIO field represents a boolean field
 * Boolean RADIO fields are identified by having exactly two options: "Yes" and "No"
 *
 * @param apField - AP custom field to check
 * @returns true if the field is a boolean RADIO field
 */
export function isApRadioBooleanField(apField: APGetCustomFieldType): boolean {
  if (apField.dataType !== "RADIO") {
    return false;
  }

  // Check if field has exactly Yes/No options
  const options = apField.textBoxListOptions;
  if (!options || options.length !== 2) {
    return false;
  }

  const labels = options.map(opt => opt.label.toLowerCase().trim());
  return labels.includes("yes") && labels.includes("no");
}

/**
 * Transform AP field value to CC format based on field types
 *
 * This is the inverse of transformBooleanValue from customFieldCreator.ts
 * Key transformation: AP RADIO "Yes"/"No" → CC boolean "true"/"false"
 * Enhanced to handle multi-value fields (arrays) for checkbox and multi-select fields
 *
 * @param value - AP field value to transform (string or array for multi-value fields)
 * @param apField - AP custom field definition for type information
 * @param ccField - CC custom field definition for validation
 * @param requestId - Request ID for logging
 * @returns Transformed value suitable for CC (comma-separated for multi-value)
 */
export function transformApToCcValue(
  value: string | string[],
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField,
  requestId: string
): string {
  const apDataType = apField.dataType;
  const ccFieldType = ccField.type.toLowerCase().trim();

  // Handle array values for multi-value fields
  if (Array.isArray(value)) {
    logDebug(
      requestId,
      `Transforming multi-value array [${value.join(', ')}] from AP ${apDataType} to CC ${ccFieldType}`
    );

    // For multi-value fields, transform each value and join with comma
    const transformedValues = value.map(singleValue =>
      transformApToCcValue(singleValue, apField, ccField, requestId)
    );
    return transformedValues.join(', ');
  }

  logDebug(
    requestId,
    `Transforming single value "${value}" from AP ${apDataType} to CC ${ccFieldType}`
  );

  // Handle AP RADIO → CC boolean conversion
  if (apDataType === "RADIO" && ccFieldType === "boolean") {
    if (isApRadioBooleanField(apField)) {
      const normalizedValue = value.toLowerCase().trim();
      
      if (normalizedValue === "yes") {
        logDebug(requestId, `Converted AP RADIO "Yes" to CC boolean "true"`);
        return "true";
      } else if (normalizedValue === "no") {
        logDebug(requestId, `Converted AP RADIO "No" to CC boolean "false"`);
        return "false";
      } else {
        logWarn(
          requestId,
          `Unexpected RADIO value "${value}" for boolean field, defaulting to "false"`
        );
        return "false";
      }
    } else {
      logWarn(
        requestId,
        `AP RADIO field "${apField.name}" doesn't have Yes/No options, treating as regular select`
      );
      return value;
    }
  }

  // Handle AP CHECKBOX → CC boolean/checkbox conversion
  if (apDataType === "CHECKBOX") {
    if (ccFieldType === "boolean") {
      // Single checkbox to boolean conversion
      const normalizedValue = value.toLowerCase().trim();

      if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
        return "true";
      } else {
        return "false";
      }
    } else if (ccFieldType === "checkbox" || ccFieldType.includes("multiselect")) {
      // Multi-checkbox to CC checkbox/multiselect - pass through value
      return value;
    }
  }

  // Handle numeric conversions
  if (apDataType === "NUMERICAL" && (ccFieldType === "number" || ccFieldType === "integer")) {
    // Ensure numeric value is properly formatted
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      return ccFieldType === "integer" ? Math.floor(numValue).toString() : numValue.toString();
    }
  }

  if (apDataType === "FLOAT" && (ccFieldType === "decimal" || ccFieldType === "float")) {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      return numValue.toString();
    }
  }

  // For most other cases, pass through the value as-is
  // This handles TEXT→text, LARGE_TEXT→textarea, DATE→date, etc.
  return value;
}

/**
 * Get the expected CC field type for an AP field type
 *
 * @param apDataType - AP field data type
 * @param apField - AP field definition (for boolean RADIO detection)
 * @returns Expected CC field type or null if no mapping exists
 */
export function getExpectedCcFieldType(
  apDataType: string,
  apField?: APGetCustomFieldType
): string | null {
  // Special handling for RADIO fields
  if (apDataType === "RADIO" && apField && isApRadioBooleanField(apField)) {
    return "boolean";
  }

  return AP_TO_CC_TYPE_MAPPING[apDataType] || null;
}

/**
 * Check if AP and CC field types are compatible for conversion
 * Enhanced validation that checks field properties for comprehensive compatibility
 *
 * @param apField - AP custom field
 * @param ccField - CC custom field
 * @param requestId - Request ID for logging
 * @returns true if types are compatible for conversion
 */
export function areFieldTypesCompatible(
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField,
  requestId: string
): boolean {
  const expectedCcType = getExpectedCcFieldType(apField.dataType, apField);
  const actualCcType = ccField.type.toLowerCase().trim();

  if (!expectedCcType) {
    logWarn(
      requestId,
      `No CC type mapping found for AP type "${apField.dataType}"`
    );
    return false;
  }

  // Basic type compatibility check
  const basicTypeCompatible = expectedCcType === actualCcType;

  if (!basicTypeCompatible) {
    logWarn(
      requestId,
      `Basic type mismatch: AP ${apField.dataType} expects CC ${expectedCcType}, but found CC ${actualCcType}`
    );
    return false;
  }

  // Enhanced compatibility checks based on field properties

  // 1. Multi-value compatibility check
  const apSupportsMultiValue = apField.dataType === "MULTIPLE_OPTIONS" || apField.dataType === "CHECKBOX";
  const ccSupportsMultiValue = ccField.allowMultipleValues === true ||
                               ccField.type.toLowerCase().includes("multiselect") ||
                               ccField.type.toLowerCase().includes("checkbox");

  if (apSupportsMultiValue && !ccSupportsMultiValue) {
    logWarn(
      requestId,
      `Multi-value mismatch: AP field "${apField.name}" supports multiple values but CC field "${ccField.label}" does not`
    );
    // Allow conversion but log warning - we'll convert to comma-separated string
    logDebug(requestId, `Proceeding with multi-value to single-value conversion (comma-separated)`);
  }

  // 2. Options compatibility for select/radio fields
  if ((apField.dataType === "SINGLE_OPTIONS" || apField.dataType === "MULTIPLE_OPTIONS" || apField.dataType === "RADIO") &&
      (ccField.type.toLowerCase().includes("select") || ccField.type.toLowerCase().includes("radio"))) {

    // Check if CC field has allowed values defined
    if (ccField.allowedValues && ccField.allowedValues.length > 0) {
      logDebug(
        requestId,
        `Options-based field compatibility: AP "${apField.name}" → CC "${ccField.label}" (${ccField.allowedValues.length} allowed values)`
      );
    } else {
      logDebug(
        requestId,
        `Options-based field without predefined values: AP "${apField.name}" → CC "${ccField.label}"`
      );
    }
  }

  // 3. Boolean field special handling
  if (apField.dataType === "RADIO" && isApRadioBooleanField(apField) && ccField.type.toLowerCase() === "boolean") {
    logDebug(
      requestId,
      `Boolean RADIO compatibility confirmed: AP "${apField.name}" (Yes/No) → CC "${ccField.label}" (boolean)`
    );
  }

  // 4. Numeric field precision compatibility
  if ((apField.dataType === "NUMERICAL" || apField.dataType === "FLOAT") &&
      (ccField.type.toLowerCase().includes("number") || ccField.type.toLowerCase().includes("decimal") || ccField.type.toLowerCase().includes("float"))) {
    logDebug(
      requestId,
      `Numeric field compatibility: AP ${apField.dataType} → CC ${ccField.type}`
    );
  }

  logDebug(
    requestId,
    `Enhanced type compatibility confirmed: AP ${apField.dataType} → CC ${actualCcType}`
  );

  return true;
}

/**
 * Log field type conversion information for debugging
 *
 * @param apField - AP custom field
 * @param ccField - CC custom field
 * @param value - Value being converted
 * @param transformedValue - Value after conversion
 * @param requestId - Request ID for logging
 */
export function logTypeConversion(
  apField: APGetCustomFieldType,
  ccField: GetCCCustomField,
  value: string,
  transformedValue: string,
  requestId: string
): void {
  const conversionInfo = [
    `Field: "${apField.name}" → "${ccField.label}"`,
    `Type: AP ${apField.dataType} → CC ${ccField.type}`,
    `Value: "${value}" → "${transformedValue}"`,
  ].join(", ");

  if (value !== transformedValue) {
    logInfo(requestId, `Type conversion applied: ${conversionInfo}`);
  } else {
    logDebug(requestId, `Type conversion (no change): ${conversionInfo}`);
  }
}
