/**
 * Custom Field Creation Utility for CC to AP Custom Fields
 *
 * Handles all custom field creation logic for synchronizing custom fields from CliniCore (CC) 
 * to AutoPatient (AP). Provides data type mapping, field validation, and creation functionality.
 *
 * Features:
 * - CC to AP data type mapping with enhanced logic
 * - Boolean field transformation to Yes/No format
 * - Options extraction for select/radio fields
 * - Field creation with proper error handling
 * - Validation before creation
 */

import type {
  APGetCustomFieldType,
  APPostCustomfieldType,
  GetCCCustomField,
} from "@type";
import { apCustomfield } from "@/apiClient";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { 
  findExistingApFieldWithApiCheck, 
  extractFieldKeyFromError, 
  findFieldByExtractedKey,
  generateApFieldKey,
  fieldNamesMatch 
} from "./fieldMatcher";

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
  // Text-based fields
  text: "TEXT",
  textarea: "LARGE_TEXT",
  string: "TEXT",

  // Numeric fields
  number: "NUMERICAL",
  integer: "NUMERICAL",
  decimal: "FLOAT",
  float: "FLOAT",
  currency: "MONETORY",
  money: "MONETORY",

  // Contact fields
  phone: "PHONE",
  telephone: "PHONE",

  // Boolean fields
  boolean: "CHECKBOX",
  checkbox: "CHECKBOX",

  // Selection fields
  select: "SINGLE_OPTIONS",
  dropdown: "SINGLE_OPTIONS",
  radio: "SINGLE_OPTIONS",
  multiselect: "MULTIPLE_OPTIONS",

  // Date/Time fields
  date: "DATE",
  datetime: "DATE",
  time: "TEXT",

  // File fields
  file: "FILE_UPLOAD",
  upload: "FILE_UPLOAD",
  attachment: "FILE_UPLOAD",

  // Signature
  signature: "SIGNATURE",

  // Default fallback
  default: "TEXT",
};

/**
 * Transform boolean field value to Yes/No format for AutoPatient
 *
 * Converts various boolean representations from CliniCore to standardized
 * Yes/No format expected by AutoPatient RADIO fields.
 *
 * @param value - The field value to transform
 * @param fieldType - The CC field type to determine if transformation is needed
 * @returns Transformed value ("Yes"/"No" for boolean fields, original value otherwise)
 */
export function transformBooleanValue(value: string, fieldType: string): string {
  const normalizedType = fieldType.toLowerCase().trim();

  if (normalizedType === "boolean") {
    const normalizedValue = value.toLowerCase().trim();

    // Handle various boolean representations
    if (
      normalizedValue === "true" ||
      normalizedValue === "1" ||
      normalizedValue === "yes"
    ) {
      return "Yes";
    } else if (
      normalizedValue === "false" ||
      normalizedValue === "0" ||
      normalizedValue === "no"
    ) {
      return "No";
    }

    // Default to "No" for any other value
    return "No";
  }

  // Return original value for non-boolean fields
  return value;
}

/**
 * Map CC custom field type to AP data type with enhanced logic
 *
 * Provides intelligent mapping between CliniCore and AutoPatient field types,
 * with special handling for boolean fields (mapped to RADIO with Yes/No options)
 * and select fields with multiple values.
 *
 * @param ccField - CliniCore custom field definition containing type and properties
 * @returns AutoPatient data type string (e.g., "RADIO", "MULTIPLE_OPTIONS", "TEXT")
 */
export function mapCcToApDataType(ccField: GetCCCustomField): string {
  const normalizedType = ccField.type.toLowerCase().trim();

  // Handle special mapping cases based on field type and properties
  if (normalizedType === "boolean") {
    // Map boolean fields to RADIO with Yes/No options
    return "RADIO";
  }

  if (normalizedType === "select" && ccField.allowMultipleValues === true) {
    return "MULTIPLE_OPTIONS";
  }

  // Use standard mapping for other cases
  return (
    CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] ||
    CC_TO_AP_DATA_TYPE_MAPPING.default
  );
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 *
 * Processes CliniCore custom field allowed values and converts them to the format
 * expected by AutoPatient custom fields. Ensures the current field value is included
 * as an option even if not in the original allowed values list.
 *
 * @param ccField - CC custom field with allowedValues array
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @param requestId - Request ID for logging correlation
 * @returns Array of option strings for AP custom field creation
 */
export function extractTextBoxListOptions(
  ccField: GetCCCustomField,
  currentValue: string,
  requestId: string
): string[] {
  const options: string[] = [];

  logDebug(
    requestId,
    `Extracting options for field ${ccField.label} (type: ${ccField.type})`
  );
  logDebug(
    requestId,
    `CC allowedValues: ${JSON.stringify(ccField.allowedValues, null, 2)}`
  );

  // Extract allowed values from CC field
  if (ccField.allowedValues && ccField.allowedValues.length > 0) {
    for (const allowedValue of ccField.allowedValues) {
      if (allowedValue.value && allowedValue.value.trim() !== "") {
        const trimmedValue = allowedValue.value.trim();
        options.push(trimmedValue);
        logDebug(requestId, `Added option: "${trimmedValue}"`);
      }
    }
  }

  // Ensure current value is included as an option if it's not already present
  if (currentValue && currentValue.trim() !== "") {
    const currentValueTrimmed = currentValue.trim();
    const existingOption = options.find(
      (option) => option.toLowerCase() === currentValueTrimmed.toLowerCase()
    );

    if (!existingOption) {
      options.push(currentValueTrimmed);
      logDebug(
        requestId,
        `Added current value as option: "${currentValueTrimmed}"`
      );
    }
  }

  // If no options were found, create a default option based on current value
  if (options.length === 0 && currentValue && currentValue.trim() !== "") {
    const trimmedValue = currentValue.trim();
    options.push(trimmedValue);
    logDebug(requestId, `Created default option: "${trimmedValue}"`);
  }

  return options;
}

/**
 * Create a new AP custom field with proper data type and options
 *
 * @param ccField - CC custom field definition
 * @param fieldValue - Current field value for option extraction
 * @param apCustomFields - Array of existing AP custom fields for validation
 * @param requestId - Request ID for logging
 * @returns Promise<APGetCustomFieldType> - Created AP custom field
 */
export async function createApCustomField(
  ccField: GetCCCustomField,
  fieldValue: string,
  apCustomFields: APGetCustomFieldType[],
  requestId: string
): Promise<APGetCustomFieldType> {
  const mappedDataType = mapCcToApDataType(ccField);
  const fieldKey = generateApFieldKey(ccField.name);

  // Simple existence check using exact matching
  const existingField = apCustomFields.find(
    (f) =>
      f.fieldKey &&
      (fieldNamesMatch(f.fieldKey, fieldKey) ||
        fieldNamesMatch(f.fieldKey, fieldKey) ||
        fieldNamesMatch(f.fieldKey, `contact.${fieldKey}`))
  );

  if (existingField) {
    logError(
      requestId,
      `FIELD MATCHING ERROR: Found existing field "${existingField.name}" (ID: ${existingField.id}, fieldKey: "${existingField.fieldKey}") that should have been detected by exact matching logic`
    );
    // Return the existing field instead of creating
    return existingField;
  }

  logInfo(
    requestId,
    `Creating new AP custom field: "${
      ccField.label
    }" with fieldKey: "${fieldKey}" (type: ${ccField.type}${
      ccField.allowMultipleValues ? " (multiple)" : ""
    } -> ${mappedDataType}${
      ccField.type.toLowerCase() === "boolean"
        ? " with Yes/No options"
        : ""
    })`
  );

  const createData: APPostCustomfieldType = {
    name: ccField.label, // Use CC label as AP name (user-friendly)
    dataType: mappedDataType,
    model: "contact", // Set model to contact as required
    fieldKey: fieldKey, // Use CC name for fieldKey (normalized)
  };

  // Add conditional properties based on data type
  if (
    mappedDataType === "RADIO" &&
    ccField.type.toLowerCase() === "boolean"
  ) {
    // For boolean fields mapped to RADIO, add Yes/No options
    createData.options = ["Yes", "No"];

    logDebug(
      requestId,
      `Adding Yes/No options for boolean field: ${ccField.label}`
    );
  } else if (
    mappedDataType === "SINGLE_OPTIONS" ||
    mappedDataType === "MULTIPLE_OPTIONS"
  ) {
    // Extract allowed values from CC field and convert to AP format
    const textBoxListOptions = extractTextBoxListOptions(
      ccField,
      fieldValue,
      requestId
    );

    if (textBoxListOptions.length > 0) {
      // Based on successful test: API expects "options" as simple string array
      createData.options = textBoxListOptions;

      logDebug(
        requestId,
        `Adding ${textBoxListOptions.length} options to ${
          ccField.label
        }: ${textBoxListOptions.join(", ")}`
      );
      logDebug(
        requestId,
        `Using simple string array for options (confirmed working structure)`
      );
    } else {
      // If no options can be created, fall back to TEXT type to avoid API error
      logDebug(
        requestId,
        `No options available for ${ccField.label}, falling back to TEXT type`
      );
      createData.dataType = "TEXT";
    }
  }

  try {
    const apCustomField = await apCustomfield.create(createData);

    // Add to our local cache to avoid duplicate creation
    apCustomFields.push(apCustomField);

    logInfo(
      requestId,
      `Successfully created AP custom field: "${ccField.label}" with ID: ${apCustomField.id}`
    );
    
    return apCustomField;
  } catch (createError: unknown) {
    const errorMessage =
      createError instanceof Error
        ? createError.message
        : String(createError);

    // Handle duplicate fieldKey error by trying to find the existing field
    if (errorMessage.includes("already exists")) {
      logInfo(
        requestId,
        `Field creation failed - already exists. Error: "${errorMessage}"`
      );
      logDebug(
        requestId,
        `Attempting to find existing field for CC field "${ccField.name}" with generated fieldKey "${fieldKey}"`
      );

      // Strategy 1: Try enhanced field finding with fresh API data
      let apCustomField = await findExistingApFieldWithApiCheck(
        apCustomFields,
        ccField,
        requestId
      );

      // Strategy 2: If not found, try extracting fieldKey from error message
      if (!apCustomField) {
        const extractedFieldKey = extractFieldKeyFromError(errorMessage);
        if (extractedFieldKey) {
          logDebug(
            requestId,
            `Extracted fieldKey "${extractedFieldKey}" from error message, searching for field`
          );

          // Refresh fields and search by extracted fieldKey
          try {
            const freshFields = await apCustomfield.all();
            apCustomField = findFieldByExtractedKey(
              freshFields,
              extractedFieldKey,
              requestId
            );

            if (apCustomField) {
              // Update cache with fresh data
              apCustomFields.length = 0;
              apCustomFields.push(...freshFields);
            }
          } catch (refreshError) {
            logError(
              requestId,
              `Failed to refresh fields for extracted fieldKey search:`,
              refreshError
            );
          }
        }
      }

      if (apCustomField) {
        logInfo(
          requestId,
          `Successfully found existing AP custom field: "${apCustomField.name}" (ID: ${apCustomField.id}, fieldKey: "${apCustomField.fieldKey}")`
        );
        return apCustomField;
      } else {
        logError(
          requestId,
          `Could not find existing field after duplicate error. CC field: "${ccField.name}", generated fieldKey: "${fieldKey}", error: "${errorMessage}"`
        );
        throw createError;
      }
    } else {
      throw createError;
    }
  }
}
