/**
 * AutoPatient Webhook Handler
 *
 * Handles incoming webhook events from AutoPatient platform.
 * Processes contact creation and update events with calendar filtering,
 * proper validation, error handling, and database logging.
 */

import type { APContactWebhookPayload, APWebhookPayload } from "@type";
import type { Context } from "hono";
import {
	contactSyncProcessor,
	validateContactWebhookPayload,
} from "@/processors/ap/contactProcessor";
import { logValidationError, logWebhookError } from "@/utils/errorLogger";
import { logInfo, logProcessingStep, logWebhook } from "@/utils/logger";

/**
 * Handle AutoPatient webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function handleAPWebhook(c: Context): Promise<Response> {
	const startTime = Date.now();
	const requestId = c.get("requestId");

	try {
		logInfo(requestId, "AP webhook received");

		// Parse JSON payload
		let payload: unknown;
		try {
			payload = await c.req.json();
		} catch (error) {
			await logValidationError(error as Error, requestId, "json_parsing");
			return c.json(
				{
					error: "Invalid JSON payload",
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Validate webhook payload structure
		let validatedPayload: APWebhookPayload;
		try {
			validatedPayload = payload as APWebhookPayload;

			// Basic validation
			if (
				!validatedPayload.contact_id ||
				!validatedPayload.date_created ||
				!validatedPayload.location
			) {
				throw new Error("Missing required fields: contact_id, date_created, or location");
			}
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"webhook_structure",
				payload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid payload structure: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Step 1: Calendar Property Check
		// If calendar property exists, skip processing (calendar event - future implementation)
		if (validatedPayload.calendar) {
			logInfo(
				requestId,
				`Calendar event detected for contact ${validatedPayload.contact_id} - skipping contact sync (future implementation)`,
			);
			return c.json(
				{
					message: "Calendar event - future implementation",
					contactId: validatedPayload.contact_id,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		}

		// Validate contact-specific payload (no calendar property)
		let contactPayload: APContactWebhookPayload;
		try {
			contactPayload = validateContactWebhookPayload(validatedPayload);
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"contact_payload",
				validatedPayload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid contact payload: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Process contact sync (creation or update)
		try {
			logWebhook(
				requestId,
				"ContactSync",
				"Contact",
				contactPayload.contact_id,
			);
			await contactSyncProcessor(requestId, contactPayload);

			const processingTime = Date.now() - startTime;
			logProcessingStep(
				requestId,
				"Contact sync completed",
				processingTime,
			);

			return c.json(
				{
					message: "Contact synced successfully",
					contactId: contactPayload.contact_id,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		} catch (error) {
			const processingTime = Date.now() - startTime;
			await logWebhookError(error as Error, requestId, {
				contactId: contactPayload.contact_id,
				processingTime,
			});

			return c.json(
				{
					error: "Contact sync failed",
					details: String(error),
					contactId: contactPayload.contact_id,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				500,
			);
		}
	} catch (error) {
		const processingTime = Date.now() - startTime;
		await logWebhookError(error as Error, requestId, {
			processingTime,
			stage: "general_processing",
		});

		return c.json(
			{
				error: "Internal server error",
				details: String(error),
				processingTime,
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
